import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';

import { User } from './entities/user.entity';
import { Farmer } from './entities/farmers/farmer.entity';
import { Buyer } from './entities/buyers/buyer.entity';
import { Admin } from './entities/admins/admin.entity';
import { Address } from '../addresses/entities/address.entity';
import { Notification } from '../notifications/entities/notification.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
import { Order } from '../orders/entities/order.entity';
import { Produce } from '../produce/entities/produce.entity';
import { Cart } from '../cart/entities/cart.entity';
import { CartItem } from '../cart/entities/cart-item.entity';
import { Preferences } from '../preferences/entities/preferences.entity';
import { Transaction } from '../transactions/entities/transaction.entity';
import { WalletsService } from 'src/wallets/wallets.service';
import { CartService } from 'src/cart/cart.service';
import { PreferencesService } from 'src/preferences/preferences.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Farmer,
      Buyer,
      Admin,
      Address,
      Notification,
      Wallet,
      Order,
      Produce,
      Cart,
      CartItem,
      Preferences,
      Transaction,
    ]),
  ],
  controllers: [UsersController],
  providers: [UsersService, WalletsService, CartService, PreferencesService],
  exports: [UsersService, TypeOrmModule],
})
export class UsersModule {}
