import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UserType } from '../shared/enums';
import { Admin, Buyer, Farmer } from './entities';
import { Produce } from '../produce/entities/produce.entity';
import { Order, OrderStatus } from '../orders/entities/order.entity';
import { Transaction, TransactionStatus } from '../transactions/entities/transaction.entity';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Buyer)
    private buyerRepository: Repository<Buyer>,
    @InjectRepository(Farmer)
    private farmerRepository: Repository<Farmer>,
    @InjectRepository(Admin)
    private adminRepository: Repository<Admin>,
    @InjectRepository(Produce)
    private produceRepository: Repository<Produce>,
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return await this.userRepository.save(user);
  }

  async findAll(): Promise<User[]> {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .orderBy('user.created_at', 'DESC')
      .getMany();
  }

  async findById(id: string): Promise<User> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .leftJoinAndSelect('user.addresses', 'addresses')
      .where('user.id = :id', { id })
      .getOne();

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .where('user.email = :email', { email })
      .getOne();

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    return user;
  }

  async findByEmailOptional(email: string): Promise<User | null> {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .where('user.email = :email', { email })
      .getOne();
  }

  async findUserByUsername(username: string): Promise<User> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .where('user.username = :username', { username })
      .getOne();

    if (!user) {
      throw new NotFoundException(`User with username ${username} not found`);
    }

    return user;
  }

  async findUserByUsernameOptional(username: string): Promise<User | null> {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .where('user.username = :username', { username })
      .getOne();
  }

  async findUserByPhone(phoneNumber: string): Promise<User> {
    const user = await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .where('user.primary_phone = :phoneNumber', { phoneNumber })
      .getOne();

    if (!user) {
      throw new NotFoundException(`User with phone ${phoneNumber} not found`);
    }

    return user;
  }

  async findUserByPhoneOptional(phoneNumber: string): Promise<User | null> {
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.farmer_profile', 'farmer')
      .leftJoinAndSelect('user.buyer_profile', 'buyer')
      .leftJoinAndSelect('user.admin_profile', 'admin')
      .where('user.primary_phone = :phoneNumber', { phoneNumber })
      .getOne();
  }

  async createBuyer(createBuyerDto: any): Promise<User> {
    // Hash password
    const hashedPassword = await bcrypt.hash(createBuyerDto.password, 10);

    // Map frontend fields to backend fields
    const userData = {
      ...createBuyerDto,
      password: hashedPassword,
      user_type: UserType.BUYER,
    };

    const user = await this.create(userData);

    // Create buyer profile
    const buyerProfile = this.buyerRepository.create({
      user_id: user.id,
      loyalty_points: 0,
    });

    await this.buyerRepository.save(buyerProfile);

    return user;
  }

  async createFarmer(createFarmerDto: any): Promise<User> {
    // Hash password
    const hashedPassword = await bcrypt.hash(createFarmerDto.password, 10);

    // Map frontend fields to backend fields
    const userData = {
      ...createFarmerDto,
      password: hashedPassword,
      user_type: UserType.FARMER,
    };

    const user = await this.create(userData);

    // Create farmer profile
    const farmerProfile = this.farmerRepository.create({
      user_id: user.id,
      farm_name: createFarmerDto.farm_name || createFarmerDto.business_name,
    });

    await this.farmerRepository.save(farmerProfile);

    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const result = await this.userRepository.update(id, updateUserDto);

    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return await this.findById(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.userRepository.delete(id);

    if (result.affected === 0) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
  }

  async updateBuyer(id: string, updateData: any): Promise<User> {
    // Separate user data from buyer-specific data
    const userUpdateData: any = {};
    const buyerUpdateData: any = {};

    // Handle nested profile data
    if (updateData.profile) {
      Object.assign(userUpdateData, updateData.profile);
    }

    // Handle nested contact data
    if (updateData.contact) {
      Object.assign(userUpdateData, updateData.contact);
    }

    // Handle nested business data
    if (updateData.business) {
      Object.assign(buyerUpdateData, updateData.business);
    }

    // Handle direct user fields
    if (updateData.username) userUpdateData.username = updateData.username;
    if (updateData.password) userUpdateData.password = updateData.password;
    if (updateData.verified !== undefined) userUpdateData.verified = updateData.verified;
    if (updateData.is_active !== undefined) userUpdateData.is_active = updateData.is_active;
    if (updateData.last_login) userUpdateData.last_login = updateData.last_login;

    // Handle buyer-specific fields
    if (updateData.loyalty_points !== undefined) buyerUpdateData.loyalty_points = updateData.loyalty_points;

    // Update user data if there are changes
    if (Object.keys(userUpdateData).length > 0) {
      await this.userRepository.update(id, userUpdateData);
    }

    // Update buyer profile if needed
    if (Object.keys(buyerUpdateData).length > 0) {
      const buyer = await this.buyerRepository.findOne({ where: { user_id: id } });
      if (buyer) {
        await this.buyerRepository.update(buyer.id, buyerUpdateData);
      }
    }

    return await this.findById(id);
  }

  async updateFarmer(id: string, updateData: any): Promise<User> {
    // Separate user data from farmer-specific data
    const userUpdateData: any = {};
    const farmerUpdateData: any = {};

    // Handle nested profile data
    if (updateData.profile) {
      Object.assign(userUpdateData, updateData.profile);
    }

    // Handle nested contact data
    if (updateData.contact) {
      Object.assign(userUpdateData, updateData.contact);
    }

    // Handle direct user fields
    if (updateData.username) userUpdateData.username = updateData.username;
    if (updateData.password) userUpdateData.password = updateData.password;
    if (updateData.verified !== undefined) userUpdateData.verified = updateData.verified;
    if (updateData.is_active !== undefined) userUpdateData.is_active = updateData.is_active;
    if (updateData.last_login) userUpdateData.last_login = updateData.last_login;

    // Handle farmer-specific fields
    if (updateData.farm_name) farmerUpdateData.farm_name = updateData.farm_name;
    if (updateData.farm_size) farmerUpdateData.farm_size = updateData.farm_size;
    if (updateData.farm_address) farmerUpdateData.farm_address = updateData.farm_address;
    if (updateData.farmer_account_number) farmerUpdateData.account_number = updateData.farmer_account_number;
    if (updateData.farmer_account_name) farmerUpdateData.account_name = updateData.farmer_account_name;
    if (updateData.farmer_bank_name) farmerUpdateData.bank_name = updateData.farmer_bank_name;
    if (updateData.farmer_bank_branch) farmerUpdateData.bank_branch = updateData.farmer_bank_branch;

    // Update user data if there are changes
    if (Object.keys(userUpdateData).length > 0) {
      await this.userRepository.update(id, userUpdateData);
    }

    // Update farmer profile if needed
    if (Object.keys(farmerUpdateData).length > 0) {
      const farmer = await this.farmerRepository.findOne({ where: { user_id: id } });
      if (farmer) {
        await this.farmerRepository.update(farmer.id, farmerUpdateData);
      }
    }

    return await this.findById(id);
  }

  async findAllBuyers(): Promise<Buyer[]> {
    return await this.buyerRepository.find({
      relations: ['user'],
      order: { created_at: 'DESC' }
    });
  }

  async findBuyerById(id: string): Promise<Buyer> {
    const buyer = await this.buyerRepository.findOne({
      where: { user_id: id },
      relations: ['user']
    });

    if (!buyer) {
      throw new NotFoundException(`Buyer with ID ${id} not found`);
    }

    return buyer;
  }

  async deleteBuyer(id: string): Promise<{ message: string }> {
    const buyer = await this.buyerRepository.findOne({ where: { user_id: id } });
    if (buyer) {
      await this.buyerRepository.delete(buyer.id);
    }
    await this.remove(id);
    return { message: 'Buyer deleted successfully' };
  }

  async findAllFarmers(): Promise<Farmer[]> {
    return await this.farmerRepository.find({
      relations: ['user'],
      order: { created_at: 'DESC' }
    });
  }

  async findFarmerById(id: string): Promise<Farmer> {
    const farmer = await this.farmerRepository.findOne({
      where: { user_id: id },
      relations: ['user']
    });

    if (!farmer) {
      throw new NotFoundException(`Farmer with ID ${id} not found`);
    }

    return farmer;
  }

  async findBuyerForProfile(id: string): Promise<any> {
    const buyer = await this.buyerRepository.findOne({
      where: { user_id: id },
      relations: ['user']
    });

    if (!buyer) {
      throw new NotFoundException(`Buyer with ID ${id} not found`);
    }

    // Transform to expected frontend structure
    return {
      id: buyer.user.id,
      username: buyer.user.username,
      loyaltyPoints: buyer.loyalty_points,
      profile: {
        firstName: buyer.user.first_name,
        lastName: buyer.user.last_name,
        middleName: buyer.user.middle_name,
        prefix: buyer.user.prefix,
        gender: buyer.user.gender,
        dateOfBirth: buyer.user.date_of_birth,
        profilePicture: buyer.user.profile_picture
      },
      contact: {
        email: buyer.user.email,
        primaryPhone: buyer.user.primary_phone,
        secondaryPhone: buyer.user.secondary_phone
      },
      business: {
        businessName: buyer.business_name,
        deliveryPreferences: buyer.delivery_preferences,
        paymentMethods: buyer.payment_methods
      },
      createdAt: buyer.user.created_at,
      updatedAt: buyer.user.updated_at,
      verified: buyer.user.verified,
      isActive: buyer.user.is_active,
      lastLogin: buyer.user.last_login
    };
  }

  async findFarmerForProfile(id: string): Promise<any> {
    const farmer = await this.farmerRepository.findOne({
      where: { user_id: id },
      relations: ['user']
    });

    if (!farmer) {
      throw new NotFoundException(`Farmer with ID ${id} not found`);
    }

    // Transform to expected frontend structure
    return {
      id: farmer.user.id,
      username: farmer.user.username,
      farmName: farmer.farm_name,
      farmAddress: farmer.farm_address,
      farmSize: farmer.farm_size,
      farmerAccountNumber: farmer.account_number,
      farmerAccountName: farmer.account_name,
      farmerBankName: farmer.bank_name,
      farmerBankBranch: farmer.bank_branch,
      profile: {
        firstName: farmer.user.first_name,
        lastName: farmer.user.last_name,
        middleName: farmer.user.middle_name,
        prefix: farmer.user.prefix,
        gender: farmer.user.gender,
        dateOfBirth: farmer.user.date_of_birth,
        profilePicture: farmer.user.profile_picture
      },
      contact: {
        email: farmer.user.email,
        primaryPhone: farmer.user.primary_phone,
        secondaryPhone: farmer.user.secondary_phone
      },
      business: {
        businessName: farmer.farm_name // Use farm_name as business name for farmers
      }
    };
  }

  async deleteFarmer(id: string): Promise<{ message: string }> {
    const farmer = await this.farmerRepository.findOne({ where: { user_id: id } });
    if (farmer) {
      await this.farmerRepository.delete(farmer.id);
    }
    await this.remove(id);
    return { message: 'Farmer deleted successfully' };
  }

  async findAllUsers(options: { skip?: number; limit?: number } = {}): Promise<User[]> {
    const { skip = 0, limit = 10 } = options;
    return await this.userRepository.find({
      skip,
      take: limit,
      order: { created_at: 'DESC' }
    });
  }

  async searchUsers(query: string, options: { skip?: number; limit?: number } = {}): Promise<User[]> {
    const { skip = 0, limit = 10 } = options;
    return await this.userRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.addresses', 'addresses')
      .where('user.first_name ILIKE :query', { query: `%${query}%` })
      .orWhere('user.last_name ILIKE :query', { query: `%${query}%` })
      .orWhere('user.email ILIKE :query', { query: `%${query}%` })
      .orWhere('user.username ILIKE :query', { query: `%${query}%` })
      .skip(skip)
      .take(limit)
      .orderBy('user.created_at', 'DESC')
      .getMany();
  }

  async getUserStats(): Promise<any> {
    const total_users = await this.userRepository.count();
    const total_buyers = await this.buyerRepository.count();
    const total_farmers = await this.farmerRepository.count();
    const total_admins = await this.adminRepository.count();

    return {
      total_users,
      total_buyers,
      total_farmers,
      total_admins
    };
  }

  async getFarmerDashboard(farmerId: string): Promise<any> {
    // Verify farmer exists
    const farmer = await this.findFarmerById(farmerId);

    // Query real data from database tables

    // 1. Total Products: Count from produces table where farmer_id matches
    const totalProducts = await this.produceRepository.count({
      where: { farmer_id: farmerId }
    });

    // 2. Active Orders: Count from orders table with active statuses for this farmer
    const activeOrders = await this.orderRepository
      .createQueryBuilder('order')
      .where('order.farmer_id = :farmerId', { farmerId })
      .andWhere('order.status IN (:...statuses)', {
        statuses: [OrderStatus.CONFIRMED, OrderStatus.PROCESSING, OrderStatus.SHIPPED]
      })
      .getCount();

    // 3. Pending Orders: Count from orders table with pending status for this farmer
    const pendingOrders = await this.orderRepository.count({
      where: {
        farmer_id: farmerId,
        status: OrderStatus.PENDING
      }
    });

    // 4. Total Revenue: Sum from completed transactions for this farmer
    const revenueResult = await this.transactionRepository
      .createQueryBuilder('transaction')
      .select('SUM(transaction.total_amount)', 'total')
      .where('transaction.farmer_id = :farmerId', { farmerId })
      .andWhere('transaction.status = :status', { status: TransactionStatus.COMPLETED })
      .getRawOne();

    const totalRevenue = parseFloat(revenueResult?.total || '0');

    // Format the statistics for frontend
    const stats = [
      {
        label: 'Total Products',
        value: totalProducts.toString()
      },
      {
        label: 'Active Orders',
        value: activeOrders.toString()
      },
      {
        label: 'Total Revenue',
        value: `₦${totalRevenue.toLocaleString('en-NG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
      },
      {
        label: 'Pending Orders',
        value: pendingOrders.toString()
      }
    ];

    return stats;
  }
}
