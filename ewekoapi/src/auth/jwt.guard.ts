import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import * as jwt from 'jsonwebtoken';
import { UserType } from 'src/shared/enums';

@Injectable()
export class EwekoAuthGuard implements CanActivate {
  constructor(private readonly configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authorizationToken = request.headers['authorization'];

    if (!authorizationToken) {
      throw new UnauthorizedException('Authorization header is missing');
    }

    const authArr = authorizationToken.split(' ');
    if (authArr.length !== 2 || authArr[0] !== 'Bearer') {
      throw new UnauthorizedException('Invalid authorization format');
    }

    const token = authArr[1];
    if (!token) {
      throw new UnauthorizedException('Authorization token is missing');
    }

    const jwtSecret = this.configService.get<string>('JWT_SECRET');

    try {
      const decodedToken = jwt.verify(token, jwtSecret);
      console.log('Logged-in user:', decodedToken);
      if (!decodedToken) {
        throw new UnauthorizedException('Invalid authorization token');
      }

      request.user = decodedToken;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid authorization token');
    }
  }
}

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<UserType[]>(
      'roles',
      context.getHandler(),
    );
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('Access denied: User not authenticated');
    }

    if (!requiredRoles.includes(user.user_type)) {
      throw new ForbiddenException('Access denied: Insufficient permissions');
    }

    return true;
  }
}
