import { Module } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationsController } from './notifications.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from 'src/users/users.service';
import { PaginationService } from 'src/shared/pagination/pagination.service';
import { User } from 'src/users/entities/user.entity';
import { Notification } from './entities/notification.entity';
import { Address } from 'src/addresses/entities/address.entity';
import { Farmer } from 'src/users/entities/farmers/farmer.entity';
import { Buyer } from 'src/users/entities/buyers/buyer.entity';
import { Admin } from 'src/users/entities/admins/admin.entity';
import { Produce } from 'src/produce/entities/produce.entity';
import { Order } from 'src/orders/entities/order.entity';
import { Transaction } from 'src/transactions/entities/transaction.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Notification,
      Address,
      Farmer,
      Buyer,
      Admin,
      Produce,
      Order,
      Transaction,
    ]),
  ],
  controllers: [NotificationsController],
  providers: [
    NotificationsService,
    UsersService,
    PaginationService,
  ],
})
export class NotificationsModule {}
