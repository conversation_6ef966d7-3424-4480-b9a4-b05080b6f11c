import {
  Injectable,
  NotFoundException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';
import { User } from '../users/entities/user.entity';
import { UsersService } from '../users/users.service';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import { isUUID } from 'class-validator';
import {
  NotificationDestination,
  NotificationTrigger,
} from '../shared/enums';
import { Twilio } from 'twilio';
import * as sendGrid from '@sendgrid/mail';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private lastNotification: Notification;
  private client: Twilio;

  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private userService: UsersService,
    private configService: ConfigService,
  ) {
    // Initialize Twilio client
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    if (accountSid && authToken) {
      this.client = new Twilio(accountSid, authToken);
    }

    // Initialize SendGrid
    const sendGridApiKey = this.configService.get<string>('SENDGRID_API_KEY');
    if (sendGridApiKey) {
      sendGrid.setApiKey(sendGridApiKey);
    }
  }

  @OnEvent('user.created', { async: true })
  async welcomeNewUser(data: any) {
    try {
      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const message = `Hello ${data.firstName}, Welcome to EwekoAggregate! Your account has been created successfully.`;

      const notificationData = {
        user_id: data.id,
        user_type: data.userType,
        subject: 'Welcome to Eweko!',
        message: message,
        trigger: NotificationTrigger.USER_REGISTRATION,
        is_read: false,
      };

      const saveNotification = this.notificationRepository.create(notificationData);
      const savedNotification = await this.notificationRepository.save(saveNotification);

      const user = await this.userService.findById(data.id);

      if (!user) {
        throw new NotFoundException(`User not found`);
      }

      if (savedNotification.id) {
        this.lastNotification = savedNotification;
        if (data.destination === NotificationDestination.EMAIL) {
          const to = data.email;
          const name = data.firstName;

          const msg = {
            to,
            from: ewekoMail,
            subject: savedNotification.subject,
            templateId: 'd-d7bf647cbc1b4bf1985f71e97b651877',
            dynamicTemplateData: {
              name,
              otp: data.code,
            },
          };
          const email_sent = await this.sendMail(msg);

          if (email_sent.statusCode === 202) {
            this.logger.log(`Welcome email sent to user: ${to}`);
          } else {
            this.logger.error(`Failed to send email to user: ${to}`);
          }
        }
      }

      return {
        success: true,
        statusCode: HttpStatus.CREATED,
        message: 'Welcome notification created successfully',
        data: savedNotification,
      };
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  @OnEvent('otp.created', { async: true })
  async otpCreated(data: any) {
    try {
      const ewekoMail = this.configService.get<string>('EWEKO_MAIL');
      const message = `Hello ${data.firstName}, kindly use the following OTP to verify your EwekoAggregate account: ${data.code}. Expires in 5mins`;

      const notificationData = {
        user_id: data.id,
        user_type: data.userType,
        subject: 'Verify Your Account',
        message: message,
        trigger: NotificationTrigger.SMS_VERIFICATION,
        is_read: false,
      };

      const saveNotification = this.notificationRepository.create(notificationData);
      const savedNotification = await this.notificationRepository.save(saveNotification);

      const user = await this.userService.findById(data.id);

      if (!user) {
        throw new NotFoundException(`User not found`);
      }

      if (savedNotification.id) {
        this.lastNotification = savedNotification;
        if (data.destination === NotificationDestination.EMAIL) {
          const to = data.email;
          const name = data.firstName;

          const msg = {
            to,
            from: ewekoMail,
            subject: savedNotification.subject,
            templateId: 'd-d7bf647cbc1b4bf1985f71e97b651877',
            dynamicTemplateData: {
              name,
              otp: data.code,
            },
          };
          const email_sent = await this.sendMail(msg);

          if (email_sent.statusCode === 202) {
            this.logger.log(`OTP email sent to user: ${to}`);
          } else {
            this.logger.error(`Failed to send OTP email to user: ${to}`);
          }
        }

        if (data.destination === NotificationDestination.SMS) {
          const phoneNumber = data.phoneNumber;
          const sms_sent = await this.sendSMS(phoneNumber, message);

          if (sms_sent.status === 'queued') {
            this.logger.log(`OTP SMS sent to user: ${phoneNumber}`);
          } else {
            this.logger.error(`Failed to send OTP SMS to user: ${phoneNumber}`);
          }
        }
      }

      return {
        success: true,
        statusCode: HttpStatus.CREATED,
        message: 'OTP notification created successfully',
        data: savedNotification,
      };
    } catch (error) {
      this.logger.error(error.message);
      return {
        success: false,
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: null,
      };
    }
  }

  async findAll(userId: string, paginationQuery: any) {
    if (!isUUID(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    const notifications = await this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user')
      .where('notification.user_id = :userId', { userId })
      .orderBy('notification.created_at', 'DESC')
      .take(paginationQuery.limit || 10)
      .skip(paginationQuery.offset || 0)
      .getMany();

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Notifications retrieved successfully',
      data: notifications,
    };
  }

  async findUnreadNotifications(userId: string, paginationQuery: any) {
    if (!isUUID(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    const notifications = await this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user')
      .where('notification.user_id = :userId', { userId })
      .andWhere('notification.is_read = :isRead', { isRead: false })
      .orderBy('notification.created_at', 'DESC')
      .take(paginationQuery.limit || 10)
      .skip(paginationQuery.offset || 0)
      .getMany();

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Unread notifications retrieved successfully',
      data: notifications,
    };
  }

  async findReadNotifications(userId: string, paginationQuery: any) {
    if (!isUUID(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    const notifications = await this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user')
      .where('notification.user_id = :userId', { userId })
      .andWhere('notification.is_read = :isRead', { isRead: true })
      .orderBy('notification.created_at', 'DESC')
      .take(paginationQuery.limit || 10)
      .skip(paginationQuery.offset || 0)
      .getMany();

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Read notifications retrieved successfully',
      data: notifications,
    };
  }

  async markAsRead(userId: string, id: string) {
    if (!isUUID(userId) || !isUUID(id)) {
      throw new NotFoundException('Invalid user ID or notification ID');
    }

    const result = await this.notificationRepository.update(
      { id, user_id: userId },
      { is_read: true }
    );

    if (result.affected === 0) {
      throw new NotFoundException('Notification not found');
    }

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Notification marked as read',
    };
  }

  async remove(userId: string, id: string) {
    if (!isUUID(userId) || !isUUID(id)) {
      throw new NotFoundException('Invalid user ID or notification ID');
    }

    const result = await this.notificationRepository.delete({
      id,
      user_id: userId,
    });

    if (result.affected === 0) {
      throw new NotFoundException('Notification not found');
    }

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Notification deleted successfully',
    };
  }

  async markAllAsRead(userId: string) {
    if (!isUUID(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    await this.notificationRepository.update(
      { user_id: userId, is_read: false },
      { is_read: true }
    );

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'All notifications marked as read',
    };
  }

  async findOne(userId: string, id: string) {
    if (!isUUID(userId) || !isUUID(id)) {
      throw new NotFoundException('Invalid user ID or notification ID');
    }

    const notification = await this.notificationRepository
      .createQueryBuilder('notification')
      .leftJoinAndSelect('notification.user', 'user')
      .where('notification.id = :id', { id })
      .andWhere('notification.user_id = :userId', { userId })
      .getOne();

    if (!notification) {
      throw new NotFoundException('Notification not found');
    }

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Notification retrieved successfully',
      data: notification,
    };
  }

  async updateReadStatus(id: string) {
    if (!isUUID(id)) {
      throw new NotFoundException('Invalid notification ID');
    }

    const result = await this.notificationRepository.update(
      { id },
      { is_read: true }
    );

    if (result.affected === 0) {
      throw new NotFoundException('Notification not found');
    }

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'Notification marked as read',
    };
  }

  async clearAll(userId: string) {
    if (!isUUID(userId)) {
      throw new NotFoundException('Invalid user ID');
    }

    await this.notificationRepository.delete({
      user_id: userId,
    });

    return {
      success: true,
      statusCode: HttpStatus.OK,
      message: 'All notifications cleared',
    };
  }

  private async sendMail(msg: any) {
    try {
      const response = await sendGrid.send(msg);
      return response[0];
    } catch (error) {
      this.logger.error('SendGrid error:', error);
      throw error;
    }
  }

  private async sendSMS(to: string, body: string) {
    try {
      const message = await this.client.messages.create({
        body,
        from: this.configService.get<string>('TWILIO_PHONE_NUMBER'),
        to,
      });
      return message;
    } catch (error) {
      this.logger.error('Twilio error:', error);
      throw error;
    }
  }
}
