import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreatePreferencesDto,
  UpdatePreferencesDto,
} from './dto/create-preference.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Preferences } from './entities/preferences.entity';

@Injectable()
export class PreferencesService {
  constructor(
    @InjectRepository(Preferences)
    private readonly preferencesRepository: Repository<Preferences>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(
    createPreferencesDto: CreatePreferencesDto,
  ): Promise<Preferences> {
    // Create new preferences with default values (transform to snake_case)
    const newPreferences = this.preferencesRepository.create({
      user_id: createPreferencesDto.user,
      receive_promotions: true,
      enable_2fa: false,
      general_updates: true,
      order_updates: true,
      transaction_updates: true,
      payment_updates: true,
      delivery_updates: true,
    });
    return await this.preferencesRepository.save(newPreferences);
  }

  async findAll(): Promise<Preferences[]> {
    return this.preferencesRepository.find();
  }

  async findOne(userId: string): Promise<Preferences> {
    let preference = await this.preferencesRepository.findOne({
      where: { user_id: userId },
    });

    // If preferences don't exist, create default ones
    if (!preference) {
      preference = await this.create({ user: userId });
    }

    return preference;
  }

  async update(
    userId: string,
    updatePreferencesDto: UpdatePreferencesDto,
  ): Promise<Preferences> {
    // Transform camelCase DTO fields to snake_case database fields
    const updateData: any = {};
    if (updatePreferencesDto.otpDestination !== undefined) updateData.otp_destination = updatePreferencesDto.otpDestination;
    if (updatePreferencesDto.receivePromotions !== undefined) updateData.receive_promotions = updatePreferencesDto.receivePromotions;
    if (updatePreferencesDto.enable2fa !== undefined) updateData.enable_2fa = updatePreferencesDto.enable2fa;
    if (updatePreferencesDto.generalUpdates !== undefined) updateData.general_updates = updatePreferencesDto.generalUpdates;
    if (updatePreferencesDto.orderUpdates !== undefined) updateData.order_updates = updatePreferencesDto.orderUpdates;
    if (updatePreferencesDto.transactionUpdates !== undefined) updateData.transaction_updates = updatePreferencesDto.transactionUpdates;
    if (updatePreferencesDto.paymentUpdates !== undefined) updateData.payment_updates = updatePreferencesDto.paymentUpdates;
    if (updatePreferencesDto.deliveryUpdates !== undefined) updateData.delivery_updates = updatePreferencesDto.deliveryUpdates;

    const result = await this.preferencesRepository.update(
      { user_id: userId },
      updateData
    );

    if (result.affected === 0) {
      throw new NotFoundException(`Preferences for this user not found.`);
    }

    return this.findOne(userId);
  }

  async remove(userId: string): Promise<void> {
    const result = await this.preferencesRepository.delete({ user_id: userId });

    if (result.affected === 0) {
      throw new NotFoundException(`Preferences not found for user ${userId}`);
    }
  }
}
