import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';

@Entity('preferences')
export class Preferences {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', unique: true })
  user_id: string;

  @Column({ type: 'enum', enum: ['EMAIL', 'SMS', 'BOTH'], default: 'EMAIL' })
  otp_destination: string;

  @Column({ default: true })
  receive_promotions: boolean;

  @Column({ default: false })
  enable_2fa: boolean;

  @Column({ default: true })
  general_updates: boolean;

  @Column({ default: true })
  order_updates: boolean;

  @Column({ default: true })
  transaction_updates: boolean;

  @Column({ default: true })
  payment_updates: boolean;

  @Column({ default: true })
  delivery_updates: boolean;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // Relationships
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;
}
