import { Modu<PERSON> } from '@nestjs/common';
import { AdminTokensService } from './admin-tokens.service';
import { AdminTokensController } from './admin-tokens.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../users/entities/user.entity';
import { Admin } from '../users/entities/admins/admin.entity';
import { AdminToken } from './entities/admin-token.entity';
import { Buyer } from '../users/entities/buyers/buyer.entity';
import { Farmer } from '../users/entities/farmers/farmer.entity';
import { Produce } from '../produce/entities/produce.entity';
import { Order } from '../orders/entities/order.entity';
import { Transaction } from '../transactions/entities/transaction.entity';
import { UsersService } from 'src/users/users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Admin, AdminToken, Buyer, Farmer, Produce, Order, Transaction]),
  ],
  controllers: [AdminTokensController],
  providers: [AdminTokensService, UsersService],
  exports: [AdminTokensService, TypeOrmModule],
})
export class AdminTokensModule {}
